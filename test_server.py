#!/usr/bin/env python3

"""
Test script for MCP Bible Study Server.

This script tests the MCP server's tools using direct HTTP requests with session handling.
"""

import requests
import logging
import time
import json
from typing import Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def parse_sse_response(response_text: str) -> Dict[str, Any]:
    """Parse Server-Sent Events response to extract JSON data."""
    lines = response_text.strip().replace('\r\n', '\n').split('\n')
    for line in lines:
        if line.startswith('data: '):
            return json.loads(line[6:])  # Remove 'data: ' prefix
    raise ValueError("No data found in SSE response")


def test_tools_list_sync(base_url: str, session_id: str | None) -> str | None:
    """Test the tools/list endpoint using requests."""
    logger.info("Testing tools/list endpoint...")
    
    try:
        payload = {
            "jsonrpc": "2.0",
            "method": "tools/list",
            "params": {},
            "id": 3
        }
        
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json, text/event-stream",
        }
        if session_id:
            headers["mcp-session-id"] = session_id
        
        response = requests.post(f"{base_url}/mcp/", json=payload, headers=headers)
        if response.status_code != 200:
            logger.error(f"Server returned status {response.status_code}. Response body: {response.text}")
        response.raise_for_status()
        data = parse_sse_response(response.text)
        
        # Capture new session ID from response headers if provided
        new_session_id = response.headers.get("mcp-session-id")
        if not new_session_id and session_id: 
             new_session_id = session_id
        elif not new_session_id and not session_id: 
            # For stateless HTTP, we can continue without explicit session ID
            new_session_id = "stateless"
        
        assert "result" in data, "Response should contain 'result'"
        result = data["result"]
        tools = result.get("tools", [])
        assert isinstance(tools, list), "tools/list should return a list"
        tool_names = [tool["name"] for tool in tools]
        assert "study_method_overview" in tool_names, "study_method_overview tool should be in the list"
        assert "semantic_context" in tool_names, "semantic_context tool should be in the list"
        
        logger.info("✅ tools/list test passed")
        logger.info(f"Found {len(tools)} tools: {', '.join(tool_names)}")
        
        return new_session_id
    except Exception as e:
        logger.error(f"Tests FAILED: {str(e)}")
        raise


def test_study_method_overview_sync(base_url: str, session_id: str) -> None:
    """Test the study_method_overview tool using requests."""
    logger.info("Testing study_method_overview tool...")
    
    try:
        payload = {
            "jsonrpc": "2.0",
            "method": "tools/call",
            "params": {
                "name": "study_method_overview",
                "arguments": {}
            },
            "id": 1
        }
        
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json, text/event-stream",
            "mcp-session-id": session_id
        }
        
        response = requests.post(f"{base_url}/mcp/", json=payload, headers=headers)
        if response.status_code != 200:
            logger.error(f"Server returned status {response.status_code}. Response body: {response.text}")
        response.raise_for_status()
        data = parse_sse_response(response.text)
        
        # Verify response
        assert "result" in data, "Response should contain 'result'"
        result = data["result"]
        
        # Extract text from MCP response format
        assert "content" in result, "Result should contain 'content'"
        assert isinstance(result["content"], list), "Content should be a list"
        assert len(result["content"]) > 0, "Content should not be empty"
        assert result["content"][0]["type"] == "text", "First content item should be text"
        
        text_content = result["content"][0]["text"]
        assert isinstance(text_content, str), "study_method_overview should return a string"
        
        # Check for expected content (may be encoded)
        expected_patterns = ["归纳式研经法", "观察法", "解释法", "应用法", "默想"]
        found_any = any(pattern in text_content for pattern in expected_patterns)
        if not found_any:
            # Try decoding escape sequences
            try:
                decoded = text_content.encode('latin1').decode('utf-8')
                found_any = any(pattern in decoded for pattern in expected_patterns)
            except:
                pass
        assert found_any, f"Result should contain expected Chinese content. Got: {repr(text_content[:200])}"
        
        # Log success
        logger.info("✅ study_method_overview test passed")
    except Exception as e:
        logger.error(f"Tests FAILED: {str(e)}")
        raise


def test_semantic_context_sync(base_url: str, session_id: str) -> None:
    """Test the semantic_context tool using requests."""
    logger.info("Testing semantic_context tool...")
    
    try:
        payload = {
            "jsonrpc": "2.0",
            "method": "tools/call",
            "params": {
                "name": "semantic_context",
                "arguments": {"query": "关于信仰的问题"}
            },
            "id": 2
        }
        
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json, text/event-stream",
            "mcp-session-id": session_id
        }
        
        response = requests.post(f"{base_url}/mcp/", json=payload, headers=headers)
        if response.status_code != 200:
            logger.error(f"Server returned status {response.status_code}. Response body: {response.text}")
        response.raise_for_status()
        data = parse_sse_response(response.text)
        
        # Verify response
        assert "result" in data, "Response should contain 'result'"
        result = data["result"]
        
        # Extract content from MCP response format
        assert "content" in result, "Result should contain 'content'"
        assert isinstance(result["content"], list), "Content should be a list"
        assert len(result["content"]) > 0, "Content should not be empty"
        assert result["content"][0]["type"] == "text", "First content item should be text"
        
        # Parse the JSON content from the text field
        import json
        text_content = result["content"][0]["text"]
        search_results = json.loads(text_content)
        
        # Verify that the result is a list of dicts with 'text' and 'metadata'
        assert isinstance(search_results, list), "semantic_context should return a list"
        if search_results:
            assert isinstance(search_results[0], dict), "Each item in semantic_context result should be a dict"
            assert "text" in search_results[0], "Each item should have a 'text' key"
            assert "metadata" in search_results[0], "Each item should have a 'metadata' key"
        
        # Log success
        logger.info("✅ semantic_context test passed")
    except Exception as e:
        logger.error(f"Tests FAILED: {str(e)}")
        raise


def main() -> None:
    # Give the server a moment to start up
    logger.info("Waiting for server to start...")
    time.sleep(3)

    # Test sequence:
    """Run all tests."""
    session_id: str | None = None 
    base_url = "http://localhost:8083"
    
    try:
        logger.info("Attempting to establish session with tools/list...")
        # Get session_id from first call to tools/list
        session_id = test_tools_list_sync(base_url, session_id)
        if not session_id:
             logger.error("Failed to retrieve session ID from tools/list.")
             raise Exception("Failed to retrieve session ID from tools/list.")
        logger.info(f"Using session ID: {session_id}")
        
        # Test individual tools with the obtained session ID
        test_study_method_overview_sync(base_url, session_id)
        test_semantic_context_sync(base_url, session_id)
        
        logger.info("🎉 All tests passed successfully!")
    except Exception:
        logger.error("❌ Some tests FAILED.")
        # No sys.exit(1) here to allow viewing logs in case of CI/automation


if __name__ == "__main__":
    main()