#!/usr/bin/env python3

"""
MCP Tools Usage Test Script.

This script demonstrates how to use the MCP server tools with example prompts
and verifies that they work as expected. It provides a more comprehensive
testing approach than the basic test_server.py script.
"""

import requests
import logging
import json
import time
import argparse
from typing import Dict, Any, List, Optional, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def parse_sse_response(response_text: str) -> Dict[str, Any]:
    """Parse Server-Sent Events response to extract JSON data."""
    lines = response_text.strip().replace('\r\n', '\n').split('\n')
    for line in lines:
        if line.startswith('data: '):
            return json.loads(line[6:])  # Remove 'data: ' prefix
    raise ValueError("No data found in SSE response")


def call_mcp_tool(
    base_url: str, 
    tool_name: str, 
    arguments: Dict[str, Any], 
    session_id: Optional[str] = None
) -> Tuple[Dict[str, Any], Optional[str]]:
    """
    Call an MCP tool with the given arguments.
    
    Args:
        base_url: The base URL of the MCP server
        tool_name: The name of the tool to call
        arguments: The arguments to pass to the tool
        session_id: Optional session ID for the MCP session
        
    Returns:
        Tuple of (response data, new session ID)
    """
    logger.info(f"Calling tool: {tool_name} with arguments: {arguments}")
    
    payload = {
        "jsonrpc": "2.0",
        "method": "tools/call",
        "params": {
            "name": tool_name,
            "arguments": arguments
        },
        "id": 1
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json, text/event-stream",
    }
    
    if session_id:
        headers["mcp-session-id"] = session_id
    
    response = requests.post(f"{base_url}/mcp/", json=payload, headers=headers)
    if response.status_code != 200:
        logger.error(f"Server returned status {response.status_code}. Response body: {response.text}")
        response.raise_for_status()
    
    data = parse_sse_response(response.text)
    new_session_id = response.headers.get("mcp-session-id", session_id)
    
    return data, new_session_id


def get_available_tools(base_url: str, session_id: Optional[str] = None) -> Tuple[List[Dict[str, Any]], Optional[str]]:
    """
    Get the list of available tools from the MCP server.
    
    Args:
        base_url: The base URL of the MCP server
        session_id: Optional session ID for the MCP session
        
    Returns:
        Tuple of (list of tools, new session ID)
    """
    logger.info("Getting available tools")
    
    payload = {
        "jsonrpc": "2.0",
        "method": "tools/list",
        "params": {},
        "id": 1
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json, text/event-stream",
    }
    
    if session_id:
        headers["mcp-session-id"] = session_id
    
    response = requests.post(f"{base_url}/mcp/", json=payload, headers=headers)
    if response.status_code != 200:
        logger.error(f"Server returned status {response.status_code}. Response body: {response.text}")
        response.raise_for_status()
    
    data = parse_sse_response(response.text)
    new_session_id = response.headers.get("mcp-session-id", session_id)
    
    tools = data.get("result", {}).get("tools", [])
    return tools, new_session_id


def extract_text_content(result: Dict[str, Any]) -> str:
    """
    Extract text content from an MCP response.
    
    Args:
        result: The result part of an MCP response
        
    Returns:
        The extracted text content
    """
    if "content" not in result:
        raise ValueError("Result does not contain 'content'")
    
    content = result["content"]
    if not isinstance(content, list) or not content:
        raise ValueError("Content is not a non-empty list")
    
    if content[0]["type"] != "text":
        raise ValueError(f"First content item is not text, got {content[0]['type']}")
    
    return content[0]["text"]


def test_study_method_overview_with_prompts(base_url: str, session_id: Optional[str] = None) -> Optional[str]:
    """
    Test the study_method_overview tool with example prompts.
    
    Args:
        base_url: The base URL of the MCP server
        session_id: Optional session ID for the MCP session
        
    Returns:
        The new session ID
    """
    logger.info("Testing study_method_overview tool with prompts")
    
    # Example prompts that might use the study_method_overview tool
    example_prompts = [
        "请介绍一下圣经研习方法",
        "我想了解如何学习圣经",
        "有哪些圣经学习的方法？",
        "请分享圣经学习的技巧"
    ]
    
    for prompt in example_prompts:
        logger.info(f"Testing with prompt: {prompt}")
        
        # For demonstration, we'll just call the tool directly
        # In a real scenario, an LLM would decide when to use this tool based on the prompt
        data, session_id = call_mcp_tool(base_url, "study_method_overview", {}, session_id)
        
        # Verify response
        if "result" not in data:
            logger.error("Response does not contain 'result'")
            continue
        
        try:
            text_content = extract_text_content(data["result"])
            
            # Check for expected content
            expected_patterns = ["归纳式研经法", "观察法", "解释法", "应用法", "默想"]
            found_patterns = [pattern for pattern in expected_patterns if pattern in text_content]
            
            if found_patterns:
                logger.info(f"✅ Found expected patterns in response: {', '.join(found_patterns)}")
            else:
                logger.warning("❌ No expected patterns found in response")
                
            # Print a snippet of the response
            logger.info(f"Response snippet: {text_content[:100]}...")
            
        except Exception as e:
            logger.error(f"Error processing response: {str(e)}")
    
    return session_id


def test_semantic_context_with_prompts(base_url: str, session_id: Optional[str] = None) -> Optional[str]:
    """
    Test the semantic_context tool with example prompts.
    
    Args:
        base_url: The base URL of the MCP server
        session_id: Optional session ID for the MCP session
        
    Returns:
        The new session ID
    """
    logger.info("Testing semantic_context tool with prompts")
    
    # Example queries that might be used with the semantic_context tool
    example_queries = [
        "关于信仰的问题",
        "圣经中的救赎",
        "如何理解圣经中的爱",
        "圣经对于生活的指导"
    ]
    
    for query in example_queries:
        logger.info(f"Testing with query: {query}")
        
        # Call the semantic_context tool
        data, session_id = call_mcp_tool(
            base_url, 
            "semantic_context", 
            {"query": query}, 
            session_id
        )
        
        # Verify response
        if "result" not in data:
            logger.error("Response does not contain 'result'")
            continue
        
        try:
            text_content = extract_text_content(data["result"])
            
            # Parse the JSON content
            search_results = json.loads(text_content)
            
            if not isinstance(search_results, list):
                logger.error("Search results is not a list")
                continue
                
            if not search_results:
                logger.warning("No search results returned")
                continue
                
            # Check the structure of search results
            if "text" in search_results[0] and "metadata" in search_results[0]:
                logger.info(f"✅ Search returned {len(search_results)} results with expected structure")
                
                # Print a snippet of the first result
                first_result_text = search_results[0]["text"]
                logger.info(f"First result snippet: {first_result_text[:100]}...")
                
                # Print metadata of the first result
                logger.info(f"First result metadata: {search_results[0]['metadata']}")
            else:
                logger.warning("❌ Search results do not have expected structure")
            
        except Exception as e:
            logger.error(f"Error processing response: {str(e)}")
    
    return session_id


def simulate_llm_with_tools(base_url: str, prompt: str, session_id: Optional[str] = None) -> Optional[str]:
    """
    Simulate an LLM using MCP tools based on the prompt.
    
    This is a simplified simulation of how an LLM might use MCP tools.
    In a real scenario, the LLM would analyze the prompt and decide which tool to use.
    
    Args:
        base_url: The base URL of the MCP server
        prompt: The user prompt
        session_id: Optional session ID for the MCP session
        
    Returns:
        The new session ID
    """
    logger.info(f"Simulating LLM with prompt: {prompt}")
    
    # Simple keyword matching to decide which tool to use
    study_method_keywords = ["方法", "学习", "研习", "技巧"]
    semantic_search_keywords = ["问题", "救赎", "理解", "指导", "爱", "信仰"]
    
    if any(keyword in prompt for keyword in study_method_keywords):
        logger.info("Using study_method_overview tool based on prompt")
        data, session_id = call_mcp_tool(base_url, "study_method_overview", {}, session_id)
        tool_used = "study_method_overview"
    elif any(keyword in prompt for keyword in semantic_search_keywords):
        logger.info("Using semantic_context tool based on prompt")
        data, session_id = call_mcp_tool(base_url, "semantic_context", {"query": prompt}, session_id)
        tool_used = "semantic_context"
    else:
        logger.info("No matching tool for prompt, defaulting to study_method_overview")
        data, session_id = call_mcp_tool(base_url, "study_method_overview", {}, session_id)
        tool_used = "study_method_overview"
    
    # Process and display the response
    if "result" in data:
        try:
            text_content = extract_text_content(data["result"])
            
            if tool_used == "semantic_context":
                search_results = json.loads(text_content)
                logger.info(f"Tool response: {len(search_results)} search results")
                if search_results:
                    logger.info(f"First result snippet: {search_results[0]['text'][:100]}...")
            else:
                logger.info(f"Tool response snippet: {text_content[:100]}...")
                
            logger.info(f"✅ Successfully used {tool_used} tool for prompt")
        except Exception as e:
            logger.error(f"Error processing response: {str(e)}")
    else:
        logger.error("Response does not contain 'result'")
    
    return session_id


def main() -> None:
    """Run the MCP tools usage tests."""
    parser = argparse.ArgumentParser(description="Test MCP server tools usage")
    parser.add_argument("--url", default="http://localhost:8080", help="MCP server URL")
    parser.add_argument("--wait", type=int, default=3, help="Seconds to wait for server startup")
    args = parser.parse_args()
    
    base_url = args.url
    
    # Give the server a moment to start up
    logger.info(f"Waiting {args.wait} seconds for server to start...")
    time.sleep(args.wait)
    
    try:
        # Get available tools
        tools, session_id = get_available_tools(base_url)
        tool_names = [tool["name"] for tool in tools]
        logger.info(f"Found {len(tools)} tools: {', '.join(tool_names)}")
        
        if not session_id:
            session_id = "stateless"
        logger.info(f"Using session ID: {session_id}")
        
        # Test study_method_overview with prompts
        session_id = test_study_method_overview_with_prompts(base_url, session_id)
        
        # Test semantic_context with prompts
        session_id = test_semantic_context_with_prompts(base_url, session_id)
        
        # Simulate an LLM using tools based on prompts
        example_prompts = [
            "请介绍一下圣经研习方法",
            "关于信仰的问题",
            "如何理解圣经中的爱",
            "圣经对于生活的指导"
        ]
        
        for prompt in example_prompts:
            session_id = simulate_llm_with_tools(base_url, prompt, session_id)
        
        logger.info("🎉 All tests completed successfully!")
        
    except Exception as e:
        logger.error(f"Tests FAILED: {str(e)}")
        raise


if __name__ == "__main__":
    main()
