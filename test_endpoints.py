#!/usr/bin/env python3

"""
Test endpoints for the Bible Study MCP Server.

This script provides direct FastAPI endpoints to test the functionality
of the MCP tools without going through the MCP protocol.
"""

import logging
import uvicorn
from fastapi import FastAPI, Query

# Import the server components we need
from server import MCP_Server

# Configure logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Create the FastAPI app
app = FastAPI(title="MCP Test Endpoints")

# Create a shared server instance
server_instance = MCP_Server()

@app.get("/")
async def root():
    """Root endpoint with information about test endpoints."""
    return {
        "message": "MCP Test Server",
        "endpoints": [
            "/test/study_method_overview - Returns Chinese Bible study methods overview",
            "/test/semantic_context - Performs semantic search on Chinese documents"
        ]
    }

@app.get("/test/study_method_overview")
async def test_study_method_overview():
    """Test endpoint for study_method_overview tool."""
    logger.info("test_study_method_overview endpoint called")
    result = server_instance._study_method_overview()
    return {"result": result}

@app.get("/test/semantic_context")
async def test_semantic_context(query: str = Query(..., description="Search query in Chinese")):
    """Test endpoint for semantic_context tool."""
    logger.info(f"test_semantic_context endpoint called with query: {query}")
    result = server_instance._semantic_context(query)
    return {"result": result}

if __name__ == "__main__":
    # Run the test server on a different port (8081)
    logger.info("Starting test endpoints server on port 8081")
    uvicorn.run(app, host="0.0.0.0", port=8081)
