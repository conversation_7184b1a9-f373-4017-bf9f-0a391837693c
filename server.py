"""
Bible Study MCP Server.

This server provides MCP protocol tools for Bible study in Chinese, including:
1. study_method_overview - Returns a hardcoded Chinese description of the Bible study method
2. semantic_context - Performs semantic search on Chinese PDFs and returns relevant paragraphs
"""

import os
import logging
from typing import Dict, List, Optional, Any, Union

from fastmcp import FastMCP
from langchain_community.document_loaders import PyMuPDFLoader
from langchain_community.vectorstores import FAISS
from langchain_community.embeddings import HuggingFaceEmbeddings, FakeEmbeddings
from langchain_text_splitters import RecursiveCharacterTextSplitter
from pydantic import BaseModel
from fastapi import Query, FastAPI

# Configure logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Define paths and constants
DOCUMENTS_DIR = os.path.join(os.path.dirname(__file__), "documents")
EMBEDDING_MODEL_NAME = "jinaai/jina-embeddings-v2-base-zh"
VECTOR_STORE_PATH = os.path.join(os.path.dirname(__file__), "vector_store")


class SemanticContextRequest(BaseModel):
    """Request model for semantic_context tool."""
    query: str


class MCP_Server:
    """MCP Server for Bible study tools."""
    
    def __init__(self):
        """Initialize the MCP server with tools."""
        # Create FastMCP instance
        # In stateless_http mode, the server will create a session if the client
        # does not provide a session ID. This is useful for the first request.
        self.mcp_app = FastMCP(stateless_http=True)
        
        # Register MCP tools
        self._register_tools()
        
        # Load embeddings and vector store
        self._load_embedding_model()
        self._load_or_create_vector_store()

    def _load_embedding_model(self):
        """Initialize the embedding model."""
        # Allow using lightweight fake embeddings during local testing to avoid large downloads
        use_fake = os.getenv("MCP_USE_FAKE_EMBEDDINGS", "false").lower() == "true"
        if use_fake:
            logger.warning("Environment variable MCP_USE_FAKE_EMBEDDINGS=true – using FakeEmbeddings for fast, offline tests")
            self.embeddings = FakeEmbeddings(size=768)  # Dimensionality doesn't matter for FAISS
            return

        logger.info(f"Loading embedding model: {EMBEDDING_MODEL_NAME}")
        self.embeddings = HuggingFaceEmbeddings(model_name=EMBEDDING_MODEL_NAME)
        logger.info("Embedding model loaded successfully")

    def _load_or_create_vector_store(self):
        """Load existing vector store or create a new one from documents."""
        if os.path.exists(VECTOR_STORE_PATH):
            logger.info("Loading existing vector store")
            try:
                self.vector_store = FAISS.load_local(VECTOR_STORE_PATH, self.embeddings)
                logger.info("Vector store loaded successfully")
                return
            except Exception as e:
                logger.error(f"Failed to load vector store: {str(e)}")
        
        logger.info("Will create a new vector store")
        self._create_vector_store()

    def _study_method_overview(self) -> str:
        """Return overview of Bible study methods in Chinese."""
        # Hardcoded Chinese string describing Bible study methods
        return """圣经学习方法有多种，包括：

1. 归纳式研经法：观察、解释和应用三个步骤，注重个人发现。
2. 专题研经法：围绕特定主题收集和分析相关经文。
3. 逐章研经法：按顺序详细学习整卷书的每一章节。
4. 人物研经法：研究圣经中特定人物的生平和教训。
5. 历史背景法：了解经文写作的历史文化背景。
6. 释经学方法：应用解经原则正确理解原文意义。
7. 灵修式读经：个人灵修时的默想和祷告式阅读。
8. 比较研经法：比较不同翻译版本和平行经文。

有效的圣经学习需要恒心、祷告和圣灵的带领。"""

    def _semantic_context(self, query: str) -> list:
        """Search for relevant context in the vector store."""
        try:
            # Ensure vector store is loaded
            if not hasattr(self, "vector_store") or self.vector_store is None:
                self._load_or_create_vector_store()
                
            # Perform semantic search
            results = self.vector_store.similarity_search(query, k=3)
            
            # Format results as list of dicts
            context = []
            for doc in results:
                context.append({
                    "text": doc.page_content,
                    "metadata": doc.metadata
                })
                
            return context
        except Exception as e:
            logger.error(f"Error during semantic search: {e}")
            raise e

    def _create_vector_store(self):
        """Create a new vector store from the documents."""
        logger.info("Creating new vector store from documents")
        
        # Check if documents directory exists
        if not os.path.exists(DOCUMENTS_DIR):
            os.makedirs(DOCUMENTS_DIR, exist_ok=True)
            logger.warning(f"Documents directory did not exist, created at {DOCUMENTS_DIR}")
        
        # Get all PDF files in the documents directory
        pdf_files = [
            os.path.join(DOCUMENTS_DIR, file)
            for file in os.listdir(DOCUMENTS_DIR)
            if file.lower().endswith('.pdf')
        ]
        
        if not pdf_files:
            logger.warning("No PDF files found in documents directory")
            self.vector_store = FAISS.from_texts(["No documents available"], self.embeddings)
            return
        
        # Load and process documents
        documents = []
        for pdf_file in pdf_files:
            try:
                logger.info(f"Processing PDF: {pdf_file}")
                loader = PyMuPDFLoader(pdf_file)
                documents.extend(loader.load())
            except Exception as e:
                logger.error(f"Error processing {pdf_file}: {e}")
        
        # Split documents into chunks
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=100,
            separators=["\n\n", "\n", "。", "，", " ", ""],
            length_function=len,
        )
        chunks = text_splitter.split_documents(documents)
        logger.info(f"Created {len(chunks)} text chunks from {len(documents)} document pages")
        
        # Create and save the vector store
        self.vector_store = FAISS.from_documents(chunks, self.embeddings)
        os.makedirs(os.path.dirname(VECTOR_STORE_PATH), exist_ok=True)
        self.vector_store.save_local(VECTOR_STORE_PATH)
        logger.info(f"Vector store created and saved to {VECTOR_STORE_PATH}")
    
    def _register_tools(self):
        """Register MCP tools."""
        # Register study_method_overview tool
        @self.mcp_app.tool()
        async def study_method_overview() -> str:
            """返回圣经研习方法的概述。"""
            # Placeholder Chinese text for the Bible study method overview
            method_overview = """
            圣经研习方法概述：

            一、观察法（What）：
            1. 仔细阅读经文，了解基本事实
            2. 找出关键词、重复的词语和主题
            3. 注意时间、地点、人物和事件的细节
            4. 寻找文学结构和上下文关系

            二、解释法（Why）：
            1. 了解历史背景和文化环境
            2. 分析词语的原意和用法
            3. 考虑文体特点（叙事、诗歌、书信等）
            4. 对照其他相关经文进行比较

            三、应用法（How）：
            1. 思考经文对原读者的意义
            2. 找出普遍原则和永恒真理
            3. 将这些原则应用到当代生活中
            4. 制定具体的实践步骤

            四、祷告默想：
            1. 以祷告开始和结束研经过程
            2. 默想经文中的属灵教训
            3. 寻求圣灵的引导和启示
            4. 谦卑接受神的话语

            五、小组讨论：
            1. 分享个人见解和领悟
            2. 倾听他人的观点和经历
            3. 一起探讨难解之处
            4. 彼此鼓励实践真理
            """
            return method_overview
        
        # Register semantic_context tool
        @self.mcp_app.tool()
        async def semantic_context(query: str) -> List[Dict[str, Any]]:
            """
            根据查询内容，返回与查询相关的圣经研习资料段落。
            
            Args:
                query: 查询字符串
            
            Returns:
                与查询相关的文本段落
            """
            logger.info(f"Semantic search query: {query}")
            
            try:
                if not hasattr(self, 'vector_store') or self.vector_store is None:
                    logger.error("Vector store not initialized")
                    raise ValueError("Vector store not initialized")
                
                # Perform similarity search
                results = self.vector_store.similarity_search(query, k=3)
                
                # Format results to match expected test schema
                context_results = []
                for doc in results:
                    context_results.append({
                        "text": doc.page_content,  # renamed key to align with tests
                        "metadata": doc.metadata,
                        "score": getattr(doc, "score", None)
                    })
                
                return context_results
            except Exception as e:
                logger.error(f"Error during semantic search: {e}")
                raise e
    
    def get_app(self) -> FastMCP:
        """Return the FastMCP app for serving."""
        logger.info("Creating FastMCP application")
        return self.mcp_app


def create_app():
    """Create the FastMCP application."""
    # Create MCP server instance that we'll use for all requests
    server_instance = MCP_Server()
    mcp_app = server_instance.get_app()
    
    # Create a simple FastAPI app for direct test endpoints
    from fastapi import FastAPI
    test_app = FastAPI()
    
    @test_app.get("/test/study_method_overview")
    async def test_study_method_overview():
        """Test endpoint for study_method_overview tool to verify functionality."""
        result = server_instance._study_method_overview()
        return {"result": result}
    
    @test_app.get("/test/semantic_context")
    async def test_semantic_context(query: str = Query(..., description="Search query in Chinese")):
        """Test endpoint for semantic_context tool to verify functionality."""
        result = server_instance._semantic_context(query)
        return {"result": result}
    
    return mcp_app


if __name__ == "__main__":
    # Default port to 8080 for local development (overridden by env var PORT in Docker)
    PORT = int(os.environ.get("PORT", 8080))
    
    # Create the MCP app
    app = create_app()
    
    # Run the server using FastMCP's HTTP-specific run method
    logger.info(f"Starting MCP server on port {PORT}")
    app.run(transport="streamable-http", host="0.0.0.0", port=PORT)
