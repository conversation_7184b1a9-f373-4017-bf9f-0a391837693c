# Bible Study MCP Server

An MCP (Model Context Protocol) server that provides context tools for a chatbot LLM using a specialized Bible study method in Chinese. The server is LLM-agnostic and designed for deployment on Google Cloud Run.

## Features

- **MCP Protocol Integration**: Exposes tools via the Model Context Protocol for easy integration with compatible LLMs
- **Semantic Search**: Performs vector-based semantic search across Chinese PDF documents
- **Bible Study Method**: Provides a structured overview of the Bible study methodology in Chinese
- **Cloud-Ready**: Optimized for deployment on Google Cloud Run with Docker

## MCP Tools

This server implements two MCP tools:

1. **study_method_overview()**: Returns a hardcoded Chinese description of the Bible study method
   - Input: None
   - Output: Chinese string describing the Bible study methodology

2. **semantic_context(query: str)**: Performs semantic search over PDF documents and returns relevant paragraphs
   - Input: Chinese query string
   - Output: One or more relevant paragraphs (in Chinese) with metadata

## Technology Stack

- **FastMCP**: Framework for implementing MCP servers
- **LangChain**: Document loading, text splitting, and vector stores
- **Hugging Face Transformers**: Chinese embedding model (jinaai/jina-embeddings-v2-base-zh)
- **FAISS**: Vector database for efficient similarity search
- **PyMuPDF**: PDF document parsing
- **Poetry**: Dependency management
- **Docker**: Containerization for deployment

## Setup

### Prerequisites

- Python 3.11+
- Poetry
- Docker (for deployment)

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/triple3-mcp-server.git
   cd triple3-mcp-server
   ```

2. Install dependencies with Poetry:
   ```bash
   poetry install
   ```

3. Add PDF documents:
   Place your Chinese Bible study PDF documents in the `documents` directory.

### Running Locally

Start the server with Poetry:

```bash
poetry run python server.py
```

By default, the server runs on port 8000. You can specify a different port using the `PORT` environment variable:

```bash
PORT=8080 poetry run python server.py
```

## Deployment to Google Cloud Run

1. Build the Docker image:
   ```bash
   docker build -t triple3-mcp-server .
   ```

2. Test the Docker image locally:
   ```bash
   docker run -p 8080:8080 triple3-mcp-server
   ```

3. Tag and push to Google Container Registry:
   ```bash
   docker tag triple3-mcp-server gcr.io/your-project-id/triple3-mcp-server
   docker push gcr.io/your-project-id/triple3-mcp-server
   ```

4. Deploy to Cloud Run:
   ```bash
   gcloud run deploy mcp-server \
     --image gcr.io/your-project-id/triple3-mcp-server \
     --platform managed \
     --memory 2Gi \
     --allow-unauthenticated
   ```

## LLM Client Integration

Any FastMCP 2.x compatible client can integrate with this MCP server. For proper connection, clients must:

1. Connect to the `/mcp/` endpoint (note the trailing slash)
2. Include proper headers in requests:
   - `Content-Type: application/json`
   - `Accept: application/json, text/event-stream`
3. Establish a session ID to maintain state

### Example Client Configuration

```python
from fastmcp import Client, HttpTransport

# Create FastMCP client
transport = HttpTransport("http://localhost:8000")
client = Client(transport=transport)

# Use the study_method_overview tool
method_overview = await client.call_tool("study_method_overview")
print(method_overview)

# Use the semantic_context tool with a query
context = await client.call_tool("semantic_context", {"query": "圣经学习方法论"}) 
print(context)
```

### JSON-RPC Example

For direct JSON-RPC interaction, follow this format:

```bash
# List available tools
curl -X POST http://localhost:8000/mcp/ \
  -H "Content-Type: application/json" \
  -H "Accept: application/json, text/event-stream" \
  -H "X-MCP-Session-ID: test-session" \
  -d '{"jsonrpc": "2.0", "method": "tools/list", "id": 1}'

# Call the study_method_overview tool
curl -X POST http://localhost:8000/mcp/ \
  -H "Content-Type: application/json" \
  -H "Accept: application/json, text/event-stream" \
  -H "X-MCP-Session-ID: test-session" \
  -d '{"jsonrpc": "2.0", "method": "tools/call", "params": {"tool": "study_method_overview"}, "id": 2}'

# Call the semantic_context tool
curl -X POST http://localhost:8000/mcp/ \
  -H "Content-Type: application/json" \
  -H "Accept: application/json, text/event-stream" \
  -H "X-MCP-Session-ID: test-session" \
  -d '{"jsonrpc": "2.0", "method": "tools/call", "params": {"tool": "semantic_context", "parameters": {"query": "圣经学习方法论"}}, "id": 3}'
```

## MCP Protocol Details

This server implements the Model Context Protocol (MCP) version 2.x, which enables LLMs to call tools via a standardized JSON-RPC interface. Key aspects:

- Tools are exposed as RPC methods under the `/mcp/` endpoint
- Session-based interaction requires a session ID header
- Supports both synchronous (JSON response) and streaming (Server-Sent Events) outputs
- Compliant with the official MCP specification for interoperability

## License

MIT License

Any MCP-compatible LLM client (e.g., Claude) can interact with this server:

1. Call `tools/list` to discover the available tools:
   ```json
   {
     "jsonrpc": "2.0",
     "method": "tools/list",
     "id": 1
   }
   ```

2. Use `tools/call` to invoke the semantic search:
   ```json
   {
     "jsonrpc": "2.0",
     "method": "tools/call",
     "params": {
       "name": "semantic_context",
       "parameters": {
         "query": "耶稣的比喻"
       }
     },
     "id": 2
   }
   ```

## Development

- Modify the placeholder text in `study_method_overview` method as needed
- To update the embedding model, change the `EMBEDDING_MODEL_NAME` constant
- Adjust chunk size and overlap parameters for your specific documents

## License

[MIT License](LICENSE)
