FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install Poetry
RUN pip install poetry

# Copy pyproject.toml and poetry.lock (if available)
COPY pyproject.toml ./
COPY poetry.lock* ./

# Configure Poetry to not create a virtual environment
RUN poetry config virtualenvs.create false

# Install dependencies
RUN poetry install --no-dev --no-interaction --no-ansi

# Copy the rest of the application
COPY . .

# Create documents directory if it doesn't exist
RUN mkdir -p /app/documents

# Pre-download the embedding model to reduce cold start time
RUN python -c "from transformers import AutoModel, AutoTokenizer; \
    model_name='jinaai/jina-embeddings-v2-base-zh'; \
    tokenizer = AutoTokenizer.from_pretrained(model_name); \
    model = AutoModel.from_pretrained(model_name)"

# Use PORT environment variable for Cloud Run
ENV PORT=8080

# Expose the port
EXPOSE 8080

# Run the server
CMD exec python server.py
